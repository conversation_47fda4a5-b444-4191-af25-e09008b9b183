# Gerçek E-posta Gönderimi Kurulumu

## 1. E-posta Konfigürasyonu Oluşturma

### <PERSON><PERSON><PERSON><PERSON> (Önerilen)
```bash
cd backend
python ../setup_email_config.py
```

Bu script size adım adım rehberlik edecek ve gerekli bilgileri soracak.

### <PERSON>
Django admin panelinden veya shell ile:

```python
from communications.models import EmailConfig

EmailConfig.objects.create(
    name='Gmail SMTP',
    smtp_server='smtp.gmail.com',
    smtp_port=587,
    use_tls=True,
    email_address='<EMAIL>',
    display_name='Your Name',
    username='<EMAIL>',
    password='your-app-password',  # Gmail App Password
    is_active=True,
    is_default=True
)
```

## 2. Gmail Kurulumu (En Yaygın)

### Adım 1: 2FA Aktifleştirin
1. Google Account > Security
2. 2-Step Verification'ı aktifleştirin

### Adım 2: App Password Oluşturun
1. Google Account > Security > App passwords
2. "Mail" seçin
3. Oluşturulan 16 haneli şifreyi kopyalayın

### Adım 3: Konfigürasyon
- **SMTP Server**: smtp.gmail.com
- **Port**: 587
- **TLS**: Evet
- **Username**: Gmail adresiniz
- **Password**: App Password (16 haneli)

## 3. SendGrid Kurulumu (Profesyonel)

### Environment Variable
```bash
export SENDGRID_API_KEY='your-sendgrid-api-key'
```

### Windows
```cmd
set SENDGRID_API_KEY=your-sendgrid-api-key
```

### .env dosyası
```
SENDGRID_API_KEY=your-sendgrid-api-key
```

## 4. Diğer E-posta Sağlayıcıları

### Outlook/Hotmail
- **SMTP Server**: smtp-mail.outlook.com
- **Port**: 587
- **TLS**: Evet

### Yahoo
- **SMTP Server**: smtp.mail.yahoo.com
- **Port**: 587 veya 465
- **TLS**: Evet

### Yandex
- **SMTP Server**: smtp.yandex.com
- **Port**: 587
- **TLS**: Evet

## 5. Test Etme

### E-posta Durumunu Kontrol
```bash
curl http://localhost:8000/api/v1/communications/messages/email-status/
```

Başarılı yanıt:
```json
{
  "active_configs": 1,
  "has_default_config": true,
  "ready_to_send": true,
  "message": "E-posta sistemi hazır"
}
```

### Frontend'den Test
1. CRM'e giriş yapın
2. İletişim > E-posta Oluştur
3. Firma seçin (zorunlu)
4. Alıcı e-posta adresi girin
5. Konu ve içerik yazın
6. "Gönder" butonuna tıklayın

## 6. Hata Giderme

### "Aktif e-posta konfigürasyonu bulunamadı"
```bash
python ../setup_email_config.py
```

### Gmail "Authentication failed"
- App Password kullandığınızdan emin olun
- 2FA aktif olmalı
- Normal Gmail şifresi çalışmaz

### SendGrid "Unauthorized"
- API Key'in doğru olduğundan emin olun
- Environment variable'ın yüklendiğini kontrol edin

### "SMTP connection failed"
- İnternet bağlantınızı kontrol edin
- Firewall/antivirus SMTP portlarını engelliyor olabilir
- SMTP ayarlarını tekrar kontrol edin

## 7. Güvenlik Notları

- **Asla** gerçek şifrelerinizi kullanmayın
- Gmail için mutlaka App Password kullanın
- Konfigürasyon şifrelerini güvenli tutun
- Production'da environment variable kullanın
- SMTP bağlantılarında TLS kullanın

## 8. Production Ayarları

### settings.py
```python
# E-posta ayarları
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD')
```

### Environment Variables
```bash
export EMAIL_HOST_USER='<EMAIL>'
export EMAIL_HOST_PASSWORD='your-app-password'
export SENDGRID_API_KEY='your-sendgrid-api-key'
```
