import { ReactNode, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import NotificationBell from '@/components/notifications/NotificationBell';
import {
  HomeIcon,
  BuildingOfficeIcon,
  EnvelopeIcon,
  LifebuoyIcon,
  UsersIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  UserCircleIcon,
  ChevronDownIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

type NavItem = {
  name: string;
  href: string;
  icon: React.ForwardRefExoticComponent<any>;
};

const navigation: NavItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Firmalar', href: '/companies', icon: BuildingOfficeIcon },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/contacts', icon: UsersIcon },
  { name: '<PERSON><PERSON><PERSON><PERSON>ar<PERSON>', href: '/opportunities', icon: LifebuoyIcon },
  { name: 'E-posta', href: '/communications', icon: EnvelopeIcon },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/events', icon: CalendarIcon },
];

interface MainLayoutProps {
  children: ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex h-screen bg-gray-50">
        {/* Sidebar */}
        <div className="hidden md:flex md:w-64 md:flex-col">
          <div className="flex flex-1 flex-col min-h-0 bg-gray-800">
            <div className="flex items-center h-16 flex-shrink-0 px-4 bg-gray-900">
              <h1 className="text-white font-bold text-lg">SadeCrm</h1>
            </div>
            <div className="flex flex-col flex-grow pt-5 overflow-y-auto">
              <nav className="flex-1 px-2 pb-4 space-y-1">
                {navigation.map((item) => {
                  const isActive = pathname === item.href || pathname?.startsWith(item.href + '/');
                  
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`${
                        isActive
                          ? 'bg-gray-900 text-white'
                          : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                      } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}
                    >
                      <item.icon
                        className={`${
                          isActive ? 'text-white' : 'text-gray-400 group-hover:text-white'
                        } mr-3 flex-shrink-0 h-6 w-6`}
                        aria-hidden="true"
                      />
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-col flex-1 overflow-hidden">
          {/* Top Header */}
          <div className="bg-white shadow">
            <div className="px-4 sm:px-6 lg:px-8">
              <div className="py-4 flex justify-between">
                <div className="flex items-center">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {navigation.find(item => pathname === item.href || pathname?.startsWith(item.href + '/'))?.name || 'SadeCrm'}
                  </h2>
                </div>
                
                {/* Notifications and User dropdown */}
                <div className="flex items-center space-x-4">
                  <NotificationBell />

                  <div className="relative">
                  <button
                    onClick={() => setUserMenuOpen(!userMenuOpen)}
                    className="flex items-center space-x-2 text-sm focus:outline-none"
                  >
                    <span className="rounded-full bg-gray-200 p-1">
                      <UserCircleIcon className="h-6 w-6 text-gray-500" aria-hidden="true" />
                    </span>
                    {user && (
                      <>
                        <span>{user.first_name} {user.last_name}</span>
                        <ChevronDownIcon className="h-4 w-4 text-gray-500" />
                      </>
                    )}
                  </button>
                  
                  {userMenuOpen && (
                    <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                      <div className="py-1" role="menu" aria-orientation="vertical">
                        <Link
                          href="/profile"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                          onClick={() => setUserMenuOpen(false)}
                        >
                          Profil
                        </Link>
                        <button
                          onClick={() => {
                            setUserMenuOpen(false);
                            logout();
                          }}
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left flex items-center"
                        >
                          <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                          Çıkış Yap
                        </button>
                      </div>
                    </div>
                  )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-auto">
            <main className="p-6">
              {children}
            </main>
          </div>
        </div>
      </div>
    </div>
  );
}
