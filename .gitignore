# ====================
# PYTHON / DJANGO
# ====================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Django-specific
*.log
*.pot
*.pyc
db.sqlite3
instance/
.env
*.env
.env.*

# Media / Static
media/
staticfiles/
static/

# VSCode & PyCharm
.vscode/
.idea/

# Pipenv / Poetry
Pipfile.lock
poetry.lock

# Virtual environment
venv/
env/
ENV/
.venv/

# Migrations
**/migrations/
!**/migrations/__init__.py

# ====================
# NODE / NEXT.JS
# ====================

# Dependency directories
node_modules/

# Build output
.next/
out/
dist/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Local env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel / Netlify deployments
.vercel/
.netlify/

# Next.js Export
*.next/

# System files
.DS_Store
Thumbs.db

# OS-specific
*.swp
*.swo
