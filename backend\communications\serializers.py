from rest_framework import serializers
from .models import EmailTemplate, EmailMessage, EmailAttachment


class EmailTemplateSerializer(serializers.ModelSerializer):
    """
    E-posta şablonları için serializer
    """
    class Meta:
        model = EmailTemplate
        fields = '__all__'


# EmailConfigSerializer kaldırıldı - SMTP ayarları artık kullanıcı profilinde


class EmailMessageListSerializer(serializers.ModelSerializer):
    """
    E-posta listesi için daha kısa serializer
    """
    company_name = serializers.SerializerMethodField()
    contact_name = serializers.SerializerMethodField()
    recipients_count = serializers.SerializerMethodField()
    
    class Meta:
        model = EmailMessage
        fields = ('id', 'subject', 'sender', 'status', 'company', 'company_name', 
                  'contact', 'contact_name', 'created_at', 'sent_at', 'recipients_count')
    
    def get_company_name(self, obj):
        if obj.company:
            return obj.company.name
        return None
    
    def get_contact_name(self, obj):
        if obj.contact:
            return str(obj.contact)
        return None
    
    def get_recipients_count(self, obj):
        return len(obj.recipients) if obj.recipients else 0


class EmailMessageDetailSerializer(serializers.ModelSerializer):
    """
    E-posta detayları için tam serializer
    """
    company_name = serializers.SerializerMethodField(read_only=True)
    contact_name = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = EmailMessage
        fields = '__all__'
    
    def get_company_name(self, obj):
        if obj.company:
            return obj.company.name
        return None
    
    def get_contact_name(self, obj):
        if obj.contact:
            return str(obj.contact)
        return None


class EmailAttachmentSerializer(serializers.ModelSerializer):
    """
    E-posta ekleri için serializer
    """
    file_size_mb = serializers.ReadOnlyField()

    class Meta:
        model = EmailAttachment
        fields = ['id', 'original_name', 'file_size', 'file_size_mb', 'content_type', 'uploaded_at']


class SendEmailSerializer(serializers.Serializer):
    """
    E-posta gönderimi için özel serializer
    """
    subject = serializers.CharField(max_length=255)
    content = serializers.CharField()
    recipients = serializers.JSONField()
    cc = serializers.JSONField(required=False)
    bcc = serializers.JSONField(required=False)
    attachments = serializers.JSONField(required=False)
    template_id = serializers.IntegerField(required=False)
    company_id = serializers.IntegerField(required=False)
    contact_id = serializers.IntegerField(required=False)
    config_id = serializers.IntegerField(required=False)  # Özel yapılandırma kullanmak için
    
    def validate_recipients(self, value):
        """
        Alıcıları doğrula ve normalize et
        """
        if not value:
            raise serializers.ValidationError("En az bir alıcı belirtilmelidir.")

        # Alıcıları normalize et
        normalized_recipients = []
        for recipient in value:
            if isinstance(recipient, dict):
                email = recipient.get('email', '').strip()
                if email:
                    normalized_recipients.append({'email': email})
            elif isinstance(recipient, str):
                email = recipient.strip()
                if email:
                    normalized_recipients.append({'email': email})

        if not normalized_recipients:
            raise serializers.ValidationError("Geçerli e-posta adresi bulunamadı.")

        return normalized_recipients

    def validate_cc(self, value):
        """
        CC alıcılarını doğrula ve normalize et
        """
        if not value:
            return []

        normalized_cc = []
        for recipient in value:
            if isinstance(recipient, dict):
                email = recipient.get('email', '').strip()
                if email:
                    normalized_cc.append({'email': email})
            elif isinstance(recipient, str):
                email = recipient.strip()
                if email:
                    normalized_cc.append({'email': email})

        return normalized_cc

    def validate_bcc(self, value):
        """
        BCC alıcılarını doğrula ve normalize et
        """
        if not value:
            return []

        normalized_bcc = []
        for recipient in value:
            if isinstance(recipient, dict):
                email = recipient.get('email', '').strip()
                if email:
                    normalized_bcc.append({'email': email})
            elif isinstance(recipient, str):
                email = recipient.strip()
                if email:
                    normalized_bcc.append({'email': email})

        return normalized_bcc

    def validate(self, data):
        # Şablon ID'si varsa şablonu kontrol et
        if data.get('template_id'):
            try:
                template = EmailTemplate.objects.get(id=data['template_id'])
            except EmailTemplate.DoesNotExist:
                raise serializers.ValidationError("Belirtilen e-posta şablonu bulunamadı.")

        # Yapılandırma ID'si varsa yapılandırmayı kontrol et
        if data.get('config_id'):
            try:
                config = EmailConfig.objects.get(id=data['config_id'], is_active=True)
            except EmailConfig.DoesNotExist:
                raise serializers.ValidationError("Belirtilen e-posta yapılandırması bulunamadı veya aktif değil.")

        return data
