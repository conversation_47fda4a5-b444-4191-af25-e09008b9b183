from django.contrib import admin
from .models import UserProfile


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    Kullanıcı profili için admin panel yapılandırması
    """
    list_display = ('user', 'sender_email', 'smtp_server', 'smtp_port')
    list_filter = ('use_tls',)
    search_fields = ('user__username', 'user__email', 'sender_email', 'smtp_server')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Kullanıcı Bilgileri', {
            'fields': ('user',)
        }),
        ('E-posta Ayarları', {
            'fields': ('sender_email', 'sender_name')
        }),
        ('SMTP Ayarları', {
            'fields': ('smtp_server', 'smtp_port', 'smtp_username', 'smtp_password', 'use_tls'),
            'description': 'E-posta gönderimi i<PERSON>in SMTP sunucu ayarları'
        }),
        ('<PERSON><PERSON> Damgaları', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        # Şifre alanını admin panelinde gizle (güvenlik için)
        readonly = list(self.readonly_fields)
        if obj:  # Düzenleme modunda
            readonly.append('smtp_password')
        return readonly
