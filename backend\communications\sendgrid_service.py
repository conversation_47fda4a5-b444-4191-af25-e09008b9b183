import os
import base64
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, Attachment, FileContent, FileName, FileType, Disposition
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import mimetypes
import logging

logger = logging.getLogger(__name__)


class SendGridEmailService:
    """
    SendGrid e-posta gönderme servisi
    """
    
    def __init__(self):
        self.api_key = None
        self.client = None

    def _ensure_client(self):
        """SendGrid client'ını lazy olarak başlat"""
        if not self.client:
            self.api_key = os.getenv('SENDGRID_API_KEY')
            if not self.api_key:
                raise ValueError("SENDGRID_API_KEY environment variable is required")
            self.client = SendGridAPIClient(api_key=self.api_key)
    
    def send_email(self, from_email, from_name, to_emails, subject, content, 
                   cc_emails=None, bcc_emails=None, attachments=None):
        """
        SendGrid ile e-posta gönder
        
        Args:
            from_email (str): Gönderen e-posta adresi
            from_name (str): <PERSON>önderen adı
            to_emails (list): Alıcı e-posta adresleri listesi
            subject (str): E-posta konusu
            content (str): E-posta içeriği (HTML)
            cc_emails (list, optional): CC alıcıları
            bcc_emails (list, optional): BCC alıcıları
            attachments (list, optional): Ek dosyalar listesi
        
        Returns:
            tuple: (success: bool, message: str, response: dict)
        """
        try:
            # Client'ı başlat
            self._ensure_client()

            # E-posta nesnesini oluştur
            message = Mail(
                from_email=(from_email, from_name),
                to_emails=to_emails,
                subject=subject,
                html_content=content
            )

            # CC alıcıları ekle
            if cc_emails:
                for cc_email in cc_emails:
                    message.add_cc(cc_email)

            # BCC alıcıları ekle
            if bcc_emails:
                for bcc_email in bcc_emails:
                    message.add_bcc(bcc_email)

            # Ekleri ekle
            if attachments:
                for attachment_data in attachments:
                    attachment = self._create_attachment(attachment_data)
                    if attachment:
                        message.add_attachment(attachment)

            # E-postayı gönder
            response = self.client.send(message)
            
            logger.info(f"Email sent successfully. Status code: {response.status_code}")
            
            return True, "E-posta başarıyla gönderildi", {
                "status_code": response.status_code,
                "headers": dict(response.headers)
            }
            
        except Exception as e:
            error_message = f"E-posta gönderilirken hata oluştu: {str(e)}"
            logger.error(error_message)
            return False, error_message, None
    
    def _create_attachment(self, attachment_data):
        """
        Ek dosya için SendGrid Attachment nesnesi oluştur
        
        Args:
            attachment_data (dict): Ek dosya bilgileri
                - file_path: Dosya yolu
                - file_name: Dosya adı
                - file_content: Dosya içeriği (base64 encoded)
        
        Returns:
            Attachment: SendGrid Attachment nesnesi
        """
        try:
            if 'file_path' in attachment_data:
                # Dosya yolundan oku
                file_path = attachment_data['file_path']
                if default_storage.exists(file_path):
                    with default_storage.open(file_path, 'rb') as f:
                        file_content = f.read()
                        encoded_content = base64.b64encode(file_content).decode()
                else:
                    logger.error(f"Attachment file not found: {file_path}")
                    return None
                    
                file_name = attachment_data.get('file_name', os.path.basename(file_path))
                
            elif 'file_content' in attachment_data:
                # Base64 encoded içerik
                encoded_content = attachment_data['file_content']
                file_name = attachment_data.get('file_name', 'attachment')
                
            else:
                logger.error("Invalid attachment data: missing file_path or file_content")
                return None
            
            # MIME type'ı belirle
            mime_type, _ = mimetypes.guess_type(file_name)
            if not mime_type:
                mime_type = 'application/octet-stream'
            
            # SendGrid Attachment nesnesi oluştur
            attachment = Attachment(
                FileContent(encoded_content),
                FileName(file_name),
                FileType(mime_type),
                Disposition('attachment')
            )
            
            return attachment
            
        except Exception as e:
            logger.error(f"Error creating attachment: {str(e)}")
            return None
    
    def validate_email_address(self, email):
        """
        E-posta adresini doğrula
        
        Args:
            email (str): E-posta adresi
        
        Returns:
            bool: Geçerli ise True
        """
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def format_recipients(self, recipients):
        """
        Alıcı listesini SendGrid formatına çevir
        
        Args:
            recipients (list): Alıcı listesi (str veya dict)
        
        Returns:
            list: Formatlanmış alıcı listesi
        """
        formatted_recipients = []
        
        for recipient in recipients:
            if isinstance(recipient, str):
                if self.validate_email_address(recipient):
                    formatted_recipients.append(recipient)
            elif isinstance(recipient, dict):
                email = recipient.get('email')
                name = recipient.get('name')
                if email and self.validate_email_address(email):
                    if name:
                        formatted_recipients.append((email, name))
                    else:
                        formatted_recipients.append(email)
        
        return formatted_recipients


# Global instance
sendgrid_service = SendGridEmailService()
